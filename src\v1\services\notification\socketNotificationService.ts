import { sendToUser, broadcast, sendToChannel, sendToGroup } from '../socket';
import { logger } from '../../utils/logger';

export interface NotificationData {
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  data?: any;
  timestamp?: Date;
}

export interface SystemNotificationData extends NotificationData {
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: string;
}

export class SocketNotificationService {
  // Send notification to specific user
  static notifyUser(userId: number, event: string, data: NotificationData) {
    try {
      sendToUser(userId, event, {
        ...data,
        timestamp: data.timestamp || new Date(),
      });
      logger.info(`Socket notification sent to user ${userId}: ${event}`);
    } catch (error) {
      logger.error(
        `Failed to send socket notification to user ${userId}:`,
        error
      );
    }
  }

  // Broadcast notification to all connected users
  static broadcastNotification(event: string, data: NotificationData) {
    try {
      broadcast(event, {
        ...data,
        timestamp: data.timestamp || new Date(),
      });
      logger.info(`Socket notification broadcasted: ${event}`);
    } catch (error) {
      logger.error(`Failed to broadcast socket notification:`, error);
    }
  }

  // Send notification to channel members
  static notifyChannel(
    channelId: number,
    event: string,
    data: NotificationData
  ) {
    try {
      sendToChannel(channelId, event, {
        ...data,
        timestamp: data.timestamp || new Date(),
      });
      logger.info(`Socket notification sent to channel ${channelId}: ${event}`);
    } catch (error) {
      logger.error(
        `Failed to send socket notification to channel ${channelId}:`,
        error
      );
    }
  }

  // Send notification to group members
  static notifyGroup(groupId: number, event: string, data: NotificationData) {
    try {
      sendToGroup(groupId, event, {
        ...data,
        timestamp: data.timestamp || new Date(),
      });
      logger.info(`Socket notification sent to group ${groupId}: ${event}`);
    } catch (error) {
      logger.error(
        `Failed to send socket notification to group ${groupId}:`,
        error
      );
    }
  }

  // Transaction-related notifications
  static notifyTransactionUpdate(userId: number, transactionData: any) {
    this.notifyUser(userId, 'transaction_update', {
      title: 'Transaction Update',
      message: `Your ${transactionData.type.toLowerCase()} has been ${transactionData.status.toLowerCase()}`,
      type:
        transactionData.status === 'SUCCESS'
          ? 'success'
          : transactionData.status === 'FAILED'
            ? 'error'
            : 'info',
      data: transactionData,
    });
  }

  // Wallet-related notifications
  static notifyWalletUpdate(userId: number, walletData: any) {
    this.notifyUser(userId, 'wallet_update', {
      title: 'Wallet Update',
      message: `Your wallet balance has been updated`,
      type: 'info',
      data: walletData,
    });
  }

  // Order-related notifications
  static notifyOrderUpdate(userId: number, orderData: any) {
    this.notifyUser(userId, 'order_update', {
      title: 'Order Update',
      message: `Your order ${orderData.orderNumber} has been ${orderData.status.toLowerCase()}`,
      type: orderData.status === 'COMPLETED' ? 'success' : 'info',
      data: orderData,
    });
  }

  // Booking-related notifications
  static notifyBookingUpdate(bookingData: any) {
    this.broadcastNotification('booking_update', {
      title: 'New Booking',
      message: `New package booking for ${bookingData.packageName}`,
      type: 'info',
      data: bookingData,
    });
  }

  // Staff-related notifications
  static notifyStaffUpdate(staffData: any) {
    this.broadcastNotification('staff_update', {
      title: 'Staff Update',
      message: `Staff member ${staffData.fullName} has been updated`,
      type: 'info',
      data: staffData,
    });
  }

  // System-wide notifications
  static notifySystemUpdate(data: SystemNotificationData) {
    this.broadcastNotification('system_notification', {
      title: data.title,
      message: data.message,
      type: data.type,
      data: {
        ...data.data,
        priority: data.priority,
        category: data.category,
      },
    });
  }

  // Game-related notifications
  static notifyGameUpdate(gameData: any) {
    this.broadcastNotification('game_update', {
      title: 'Game Update',
      message: `Game "${gameData.title}" has been ${gameData.status.toLowerCase()}`,
      type: 'info',
      data: gameData,
    });
  }

  // Referral-related notifications
  static notifyReferralUpdate(referralData: any) {
    this.broadcastNotification('referral_update', {
      title: 'Referral Update',
      message: `Referral ${referralData.referralID} has been ${referralData.status.toLowerCase()}`,
      type: 'info',
      data: referralData,
    });
  }

  // Reward notifications
  static notifyRewardReceived(userId: number, rewardData: any) {
    this.notifyUser(userId, 'reward_received', {
      title: 'Reward Received!',
      message: `Congratulations! You've received a reward of ${rewardData.amount}`,
      type: 'success',
      data: rewardData,
    });
  }

  // Emergency/Critical notifications
  static notifyEmergency(data: NotificationData) {
    this.broadcastNotification('emergency_notification', {
      ...data,
      type: 'error',
    });
  }

  // Maintenance notifications
  static notifyMaintenance(data: NotificationData) {
    this.broadcastNotification('maintenance_notification', {
      ...data,
      type: 'warning',
    });
  }

  // Multiple user notifications
  static notifyMultipleUsers(
    userIds: number[],
    event: string,
    data: NotificationData
  ) {
    userIds.forEach((userId) => {
      this.notifyUser(userId, event, data);
    });
  }

  // Location-based notifications (for staff in specific locations)
  static notifyLocationStaff(
    locationId: number,
    event: string,
    data: NotificationData
  ) {
    // This would require additional logic to get staff by location
    // For now, we'll broadcast with location filter in the data
    this.broadcastNotification(event, {
      ...data,
      data: {
        ...data.data,
        locationId,
        locationSpecific: true,
      },
    });
  }

  // Department-based notifications
  static notifyDepartmentStaff(
    departmentId: number,
    event: string,
    data: NotificationData
  ) {
    this.broadcastNotification(event, {
      ...data,
      data: {
        ...data.data,
        departmentId,
        departmentSpecific: true,
      },
    });
  }

  // Role-based notifications
  static notifyByRole(role: string, event: string, data: NotificationData) {
    this.broadcastNotification(event, {
      ...data,
      data: {
        ...data.data,
        targetRole: role,
        roleSpecific: true,
      },
    });
  }
}
