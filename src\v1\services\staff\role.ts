import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import { formatString } from '../../utils/stringFormatter';
import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { deleteCacheByPattern } from '../../utils/cache';

// Helper function to clear all admin-related caches
export const clearStaffCaches = async (): Promise<void> => {
  await deleteCacheByPattern('staff:*');
};

export const roleService = {
  getAllRoles: async (staffId: any, query: any) => {
    await staffHasPermission(staffId, PERMISSIONS.ROLE_VIEW);
    return db.role.findMany({
      include: {
        permissions: true,
      },
    });
  },

  createRole: async (staffId: any, reqBody: any) => {
    await staffHasPermission(staffId, PERMISSIONS.ROLE_CREATE);
    const { name, description, permissionIds } = reqBody;
    const formattedString = formatString.trimString(name);

    const checkRoleExist = await db.role.findUnique({
      where: { name: formattedString },
    });

    if (checkRoleExist) {
      throw new HttpError('Role with the name already exists', 400);
    }

    await db.role.create({
      data: {
        name,
        description,
        permissions: {
          connect: permissionIds.map((id: number) => ({ id })),
        },
      },
    });

    clearStaffCaches();

    return {
      message: 'Role created successfully',
    };
  },

  updateRole: async (staffId: any, reqBody: any) => {
    await staffHasPermission(staffId, PERMISSIONS.ROLE_EDIT);
    const { roleId, description, permissionIds } = reqBody;

    // Build update object dynamically
    const updateData: any = {};

    if (description !== undefined) updateData.description = description;
    if (permissionIds !== undefined) {
      updateData.permissions = {
        set: permissionIds.map((id: number) => ({ id })),
      };
    }

    const role = await db.role.update({
      where: { id: roleId },
      data: updateData,
    });
    clearStaffCaches();

    return {
      message: `${role.name} - Role updated successfully`,
    };
  },

  getAllPermission: async (staffId: any, query: any) => {
    await staffHasPermission(staffId, PERMISSIONS.PERMISSION_VIEW);
    return db.permission.findMany();
  },
  createPermission: async (staffId: any, reqBody: any) => {
    await staffHasPermission(staffId, PERMISSIONS.ROLE_CREATE);
    const { action, description } = reqBody;
    const formattedString = formatString.trimString(action);

    const checkExist = await db.permission.findUnique({
      where: { action: formattedString },
    });

    if (checkExist) {
      throw new HttpError('Permission already exists', 400);
    }

    await db.permission.create({
      data: {
        action,
        description,
      },
    });

    return {
      message: 'Role created successfully',
    };
  },
};
