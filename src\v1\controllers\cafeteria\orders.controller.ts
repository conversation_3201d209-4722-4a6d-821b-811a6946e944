import { Request, Response } from 'express';
import {
  orderService,
  specialOrderService,
} from '../../services/cafeteria/orders';
import { controllerOperations } from '../handlers/handleController';
import { csvControllerOperations } from '../handlers/csvController';

const staffPayment = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(orderService.checkStaffPayment, req.body, res, staffId);
};

const createOrder = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(orderService.createOrder, req.body, res, staffId);
};

export const getOrderById = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  const { id } = req.params;
  controllerOperations(orderService.getOrderById, id, res, staffId);
};

const getAllOrders = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  csvControllerOperations(orderService.getAllOrders, req.query, res, staffId);
};

const getAllStaffOrders = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  csvControllerOperations(
    orderService.getAllStaffOrders,
    req.query,
    res,
    staffId
  );
};

//Special orders
const createSpecialOrder = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    specialOrderService.createSpecialOrder,
    req.body,
    res,
    staffId
  );
};

const getSpecialOrders = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    specialOrderService.getAllSpecialOrders,
    req.query,
    res,
    staffId
  );
};

const getStaffSpecialOrders = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    specialOrderService.getStaffSpecialOrders,
    req.query,
    res,
    staffId
  );
};

const updateSpecialOrder = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    specialOrderService.updateSpecialOrder,
    req.body,
    res,
    staffId
  );
};

const receiveSpecialOrder = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    specialOrderService.receiveSpecialOrder,
    req.body,
    res,
    staffId
  );
};

const returnSpecialOrderItems = (req: Request, res: Response) => {
  const staffId = req.Staff as unknown as number;
  controllerOperations(
    specialOrderService.returnSpecialOrderItems,
    req.body,
    res,
    staffId
  );
};

export const ordersController = {
  staffPayment,
  getOrderById,
  createOrder,
  getAllOrders,
  getAllStaffOrders,
  createSpecialOrder,
  getSpecialOrders,
  updateSpecialOrder,
  getStaffSpecialOrders,
  receiveSpecialOrder,
  returnSpecialOrderItems,
};
