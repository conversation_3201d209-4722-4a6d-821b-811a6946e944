// import { resetStaffMonthlyCreditUsed, manualStaffCreditReset } from '../creditReset';
// import { db } from '../../../utils/model';
// import { staffHasPermission, PERMISSIONS } from '../../../utils/permission';
// import { HttpError } from '../../../utils/httpError';

// // Mock dependencies
// jest.mock('../../../utils/model');
// jest.mock('../../../utils/permission');
// jest.mock('../../../utils/logger');

// const mockDb = db as jest.Mocked<typeof db>;
// const mockStaffHasPermission = staffHasPermission as jest.MockedFunction<typeof staffHasPermission>;

// describe('Staff Credit Reset Service', () => {
//   beforeEach(() => {
//     jest.clearAllMocks();
//   });

//   describe('resetStaffMonthlyCreditUsed', () => {
//     it('should reset monthly credit usage for all active staff', async () => {
//       // Arrange
//       const mockUpdateResult = { count: 5 };
//       mockDb.staff.updateMany.mockResolvedValue(mockUpdateResult);

//       // Act
//       const result = await resetStaffMonthlyCreditUsed();

//       // Assert
//       expect(mockDb.staff.updateMany).toHaveBeenCalledWith({
//         where: {
//           isActive: true,
//         },
//         data: {
//           monthlyCreditUsed: 0.0,
//         },
//       });
//       expect(result).toBe(5);
//     });

//     it('should handle database errors', async () => {
//       // Arrange
//       const mockError = new Error('Database connection failed');
//       mockDb.staff.updateMany.mockRejectedValue(mockError);

//       // Act & Assert
//       await expect(resetStaffMonthlyCreditUsed()).rejects.toThrow('Database connection failed');
//     });

//     it('should return 0 when no staff records are updated', async () => {
//       // Arrange
//       const mockUpdateResult = { count: 0 };
//       mockDb.staff.updateMany.mockResolvedValue(mockUpdateResult);

//       // Act
//       const result = await resetStaffMonthlyCreditUsed();

//       // Assert
//       expect(result).toBe(0);
//     });
//   });

//   describe('manualStaffCreditReset', () => {
//     it('should reset credit usage when staff has permission', async () => {
//       // Arrange
//       const staffId = 1;
//       const mockUpdateResult = { count: 3 };
//       mockStaffHasPermission.mockResolvedValue(undefined);
//       mockDb.staff.updateMany.mockResolvedValue(mockUpdateResult);

//       // Act
//       const result = await manualStaffCreditReset(staffId);

//       // Assert
//       expect(mockStaffHasPermission).toHaveBeenCalledWith(staffId, PERMISSIONS.STAFF_EDIT);
//       expect(result).toEqual({
//         message: 'Successfully reset monthly credit usage for 3 staff members',
//         count: 3,
//       });
//     });

//     it('should throw HttpError when database operation fails', async () => {
//       // Arrange
//       const staffId = 1;
//       const mockError = new Error('Database error');
//       mockStaffHasPermission.mockResolvedValue(undefined);
//       mockDb.staff.updateMany.mockRejectedValue(mockError);

//       // Act & Assert
//       await expect(manualStaffCreditReset(staffId)).rejects.toThrow(HttpError);
//       await expect(manualStaffCreditReset(staffId)).rejects.toThrow('Failed to reset staff monthly credit usage');
//     });

//     it('should throw error when staff lacks permission', async () => {
//       // Arrange
//       const staffId = 1;
//       const permissionError = new HttpError('Insufficient permissions', 403);
//       mockStaffHasPermission.mockRejectedValue(permissionError);

//       // Act & Assert
//       await expect(manualStaffCreditReset(staffId)).rejects.toThrow(permissionError);
//       expect(mockDb.staff.updateMany).not.toHaveBeenCalled();
//     });
//   });
// });
