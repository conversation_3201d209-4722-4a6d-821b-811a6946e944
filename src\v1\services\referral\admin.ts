import * as bcrypt from 'bcryptjs';
import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import jwt, { Secret } from 'jsonwebtoken';
import { formatString } from '../../utils/stringFormatter';
import config from '../../../config/app.config';
import crypto from 'crypto';
import {
  getCache,
  setCache,
  deleteCache,
  deleteCacheByPattern,
} from '../../utils/cache';
import { devLog } from '../../utils/logger';
import { createDateFilter } from '../../utils/util';
import { enqueueSendEmailJob } from '../../jobs/queueJobs/queues/emailQueueJob';
import {
  staffHasPermission,
  PERMISSIONS,
  createStaffAuthHelper,
} from '../../utils/permission';

export const SECRET_KEY = config.SIGNING_TOKEN_SECRET as Secret;

// Helper function to clear all admin-related caches
export const clearAdminCaches = async (): Promise<void> => {
  await deleteCacheByPattern('referral:*');
};

export const adminReferralService = {
  // Get all referrals with pagination, search, and caching
  getAllReferrals: async (staffId: any, query: any = {}) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(PERMISSIONS.REFERRAL_VIEW);
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }

    const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
    const locationId = hasLocationAll ? undefined : await auth.getLocationId();

    const page: number = parseInt(query.page as string) || 1;
    const limit: number = parseInt(query.limit as string) || 10;
    const search: string = (query.search as string) || '';
    const status = query.status;
    const startDate = query.startDate as string;
    const endDate = query.endDate as string;
    const dateFilter = createDateFilter(startDate, endDate);

    const whereClause: any = {
      ...(search
        ? {
            OR: [
              { patientFirstName: { contains: search } },
              { patientLastName: { contains: search } },
              { patientPhoneNumber: { contains: search } },
              { patientEmail: { contains: search } },
              { internalDoctor: { fullName: { contains: search } } },
              { referringEntity: { name: { contains: search } } },
            ],
          }
        : {}),
      ...(locationId !== undefined && locationId !== null
        ? { locationId }
        : {}),
      ...(status ? { status: status.toUpperCase() } : {}),
      ...dateFilter,
    };

    const [referrals, totalCount] = await db.$transaction([
      db.externalReferral.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          receivingEntity: {
            select: {
              name: true,
              phoneNumber: true,
              email: true,
              entityType: true,
            },
          },
          referringEntity: {
            select: {
              name: true,
              phoneNumber: true,
              email: true,
              entityType: true,
            },
          },
          patient: {
            select: {
              firstName: true,
              lastName: true,
              emailAddress: true,
            },
          },
        },
      }),
      db.externalReferral.count({
        where: whereClause,
      }),
    ]);

    const response = {
      referrals: referrals,
      totalPages: Math.ceil(totalCount / limit),
      totalCount: totalCount,
      currentPage: page,
      limit: limit,
    };

    return response;
  },

  //Get all referring Doctors
  getAllReferringEntities: async (staffId: any, query: any = {}) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(PERMISSIONS.REFERRAL_VIEW);
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }

    const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
    const locationId = hasLocationAll ? undefined : await auth.getLocationId();

    const page: number = parseInt(query.page as string) || 1;
    const limit: number = parseInt(query.limit as string) || 10;
    const search: string = (query.search as string) || '';

    const whereClause: any = {
      ...(search
        ? {
            OR: [
              { email: { contains: search } },
              { name: { contains: search } },
              { PhoneNumber: { contains: search } },
            ],
          }
        : {}),
      ...(locationId !== undefined && locationId !== null
        ? { locationId }
        : {}),
    };

    const [referers, totalCount] = await db.$transaction([
      db.referringEntity.findMany({
        where: whereClause,
        include: {
          submittedCredentials: true,
          specialty: true,
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      db.referringEntity.count({
        where: whereClause,
      }),
    ]);

    const sanitizedResult = referers.map(
      ({ password, resetPasswordToken, ...obj }) => obj
    );

    const response = {
      referers: sanitizedResult,
      totalPages: Math.ceil(totalCount / limit),
      totalCount: totalCount,
      currentPage: page,
      limit: limit,
    };

    return response;
  },

  // Get all referrals with pagination, search, and caching
  getAllReferralReward: async (staffId: any, query: any = {}) => {
    await staffHasPermission(staffId, PERMISSIONS.REWARD_VIEW);

    const page: number = parseInt(query.page as string) || 1;
    const limit: number = parseInt(query.limit as string) || 10;
    const search: string = (query.search as string) || '';
    const startDate = query.startDate as string;
    const endDate = query.endDate as string;
    const dateFilter = createDateFilter(startDate, endDate);

    const whereClause: any = {
      ...(search
        ? {
            OR: [
              { referringEntity: { name: { contains: search } } },
              { referringEntity: { email: { contains: search } } },
            ],
          }
        : {}),
      ...dateFilter,
    };

    const [logs, totalCount] = await db.$transaction([
      db.referralRewardLog.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          referringEntity: {
            select: {
              name: true,
              phoneNumber: true,
              email: true,
              entityType: true,
            },
          },
        },
      }),
      db.referralRewardLog.count({
        where: whereClause,
      }),
    ]);

    const response = {
      rewardLogs: logs,
      totalPages: Math.ceil(totalCount / limit),
      totalCount: totalCount,
      currentPage: page,
      limit: limit,
    };

    return response;
  },

  // Register referrer
  getPublicReferralEntities: async (accountId: any, query: any = {}) => {
    const category = query.category;
    const whereClause: any = {
      isActive: true,
      canAcceptReferral: true,
      ...(category ? { entityCategory: category.toUpperCase() } : {}),
    };
    const referers = await db.referringEntity.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
      },
    });

    return referers;
  },

  singleReferralAdmin: async (staffId: any, referralId: any) => {
    const auth = createStaffAuthHelper(staffId);
    const canManage = await auth.hasPermission(PERMISSIONS.REFERRAL_VIEW);
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }

    const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
    const locationId = hasLocationAll ? undefined : await auth.getLocationId();

    const cacheKey = `referral:admin:single:${referralId}`;
    const cachedReferral = await getCache<any>(cacheKey);

    if (cachedReferral) {
      devLog(`Cache hit for referral ${referralId}`);
      return cachedReferral;
    }

    // If not in cache, fetch from database
    devLog(`Cache miss for referral ${referralId}, fetching from database`);
    const referral = await db.externalReferral.findFirst({
      where: {
        id: Number(referralId),
        ...(locationId !== undefined && locationId !== null
          ? { locationId: Number(locationId) }
          : {}),
      },
      include: {
        comments: true,
        patient: {
          select: {
            emailAddress: true,
            phoneNumber: true,
            firstName: true,
            lastName: true,
            dateOfBirth: true,
          },
        },
        receivingEntity: {
          select: {
            name: true,
            email: true,
            phoneNumber: true,
            primaryContactEmail: true,
            primaryContactName: true,
            primaryContactPhone: true,
            entityType: true,
          },
        },
        referringEntity: {
          select: {
            name: true,
            email: true,
            phoneNumber: true,
            primaryContactEmail: true,
            primaryContactName: true,
            primaryContactPhone: true,
            entityType: true,
          },
        },
      },
    });

    if (!referral) {
      throw new HttpError('referral not found', 400);
    }

    return referral;
  },
};
