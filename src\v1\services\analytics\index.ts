import { db } from '../../utils/model';
// import { adminHasPermission, PERMISSIONS } from '../admin/permission';
// import { staffHasPermission, PERMISSIONS } from '../../utils/permission';
import { months } from '../../utils/util';
import { HttpError } from '../../utils/httpError';
import {
  staffHasPermission,
  PERMISSIONS,
  createStaffAuthHelper,
} from '../../utils/permission';

export const analyticsService = {
  getDiscountAnalytics: async (staffId: any, query: any) => {
    try {
      const auth = createStaffAuthHelper(staffId);

      const canManage = await auth.hasPermission(PERMISSIONS.REWARD_VIEW);
      if (!canManage) {
        throw new HttpError('Unauthorized', 403);
      }

      const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
      const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

      let locationFilter: any = {};
      if (hasLocationAll) {
        // No location filter - get all data
      } else if (hasRegion) {
        const regionId = await auth.getRegionId();
        locationFilter = {
          location: {
            regionId,
          },
        };
      } else {
        const locationId = await auth.getLocationId();
        locationFilter = { locationId };
      }

      // Get year from query or use current year as default
      let selectedYear = query?.year;
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();

      if (!selectedYear || isNaN(Number(selectedYear))) {
        selectedYear = currentYear;
      } else {
        selectedYear = Number(selectedYear);
      }

      // Create date range for the selected year
      const yearStart = new Date(selectedYear, 0, 1);
      const yearEnd = new Date(selectedYear, 11, 31, 23, 59, 59);

      // Get all discount modifiers
      const discountModifiers = await db.packagePriceModifier.findMany({
        select: {
          id: true,
          modifierCode: true,
          amount: true,
          percentage: true,
          isActive: true,
          createdAt: true,
        },
      });

      // Get all discount records for the selected year
      const discountRecords = await db.discountRecord.findMany({
        where: {
          createdAt: {
            gte: yearStart,
            lte: yearEnd,
          },
          ...locationFilter,
        },
        select: {
          id: true,
          createdAt: true,
          code: true,
          discountAmount: true,
        },
      });

      // Calculate basic metrics
      const totalDiscounts = discountModifiers.length;
      const activeDiscounts = discountModifiers.filter(
        (d) => d.isActive
      ).length;
      const totalUsage = discountRecords.length;
      const savingsGenerated = discountRecords.reduce(
        (sum, record) => sum + Number(record.discountAmount),
        0
      );

      // Group discounts by type (Amount vs Percentage)
      const discountTypes = [
        {
          type: 'Amount',
          count: 0,
          usage: 0,
        },
        {
          type: 'Percentage',
          count: 0,
          usage: 0,
        },
      ];

      // Create a map to track usage by discount code
      const discountUsageMap = new Map();

      // Initialize the map with all discount codes
      discountModifiers.forEach((modifier) => {
        discountUsageMap.set(modifier.modifierCode, {
          code: modifier.modifierCode,
          usage: 0,
          savings: 0,
          isPercentage: modifier.percentage !== null,
        });
      });

      // Count discount types
      discountModifiers.forEach((modifier) => {
        if (modifier.percentage !== null) {
          discountTypes[1].count += 1; // Percentage type
        } else {
          discountTypes[0].count += 1; // Amount type
        }
      });

      // Process discount records to calculate usage and savings by code
      discountRecords.forEach((record) => {
        // Update usage count for the discount type
        const discountInfo = discountUsageMap.get(record.code);

        if (discountInfo) {
          discountInfo.usage += 1;
          discountInfo.savings += Number(record.discountAmount);

          // Update usage count in discountTypes
          const typeIndex = discountInfo.isPercentage ? 1 : 0;
          discountTypes[typeIndex].usage += 1;
        }
      });

      // Get top discounts by usage
      const topDiscounts = Array.from(discountUsageMap.values())
        .filter((d) => d.usage > 0)
        .sort((a, b) => b.usage - a.usage)
        .slice(0, 5)
        .map(({ code, usage, savings }) => ({ code, usage, savings }));

      // Generate monthly data
      const monthlyData = Array.from({ length: 12 }, (_, i) => ({
        month: months[i],
        usage: 0,
        savings: 0,
      }));

      // Process discount records to populate monthly data
      discountRecords.forEach((record) => {
        const month = new Date(record.createdAt).getMonth();
        monthlyData[month].usage += 1;
        monthlyData[month].savings += Number(record.discountAmount);
      });

      return {
        data: {
          year: selectedYear,
          totalDiscounts,
          activeDiscounts,
          totalUsage,
          savingsGenerated,
          discountTypes,
          topDiscounts,
          monthlyData,
        },
      };
    } catch (error) {
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch discount analytics', 500);
    }
  },

  getTransactionAnalytics: async (staffId: any, query: any) => {
    try {
      const auth = createStaffAuthHelper(staffId);

      const canManage = await auth.hasPermission(PERMISSIONS.TRANSACTION_VIEW);
      if (!canManage) {
        throw new HttpError('Unauthorized', 403);
      }

      const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
      const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

      let locationFilter: any = {};
      if (hasLocationAll) {
        // No location filter - get all data
      } else if (hasRegion) {
        const regionId = await auth.getRegionId();
        locationFilter = {
          location: {
            regionId,
          },
        };
      } else {
        const locationId = await auth.getLocationId();
        locationFilter = { locationId };
      }

      // Get year from query or use current year as default
      let selectedYear = query?.year;
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();

      if (!selectedYear || isNaN(Number(selectedYear))) {
        selectedYear = currentYear;
      } else {
        selectedYear = Number(selectedYear);
      }

      // Create date range for the selected year
      const yearStart = new Date(selectedYear, 0, 1);
      const yearEnd = new Date(selectedYear, 11, 31, 23, 59, 59);

      // Get all transactions for the selected year
      const transactions = await db.transaction.findMany({
        where: {
          createdAt: {
            gte: yearStart,
            lte: yearEnd,
          },
          ...locationFilter,
        },
        select: {
          id: true,
          createdAt: true,
          amount: true,
          type: true,
          status: true,
        },
      });

      // Calculate total transactions and amount
      const totalTransactions = transactions.length;
      const totalAmount = transactions.reduce(
        (sum, transaction) => sum + Number(transaction.amount),
        0
      );

      // Group transactions by type
      const transactionTypes = [
        {
          type: 'INBOUND',
          count: 0,
          amount: 0,
        },
        {
          type: 'OUTBOUND',
          count: 0,
          amount: 0,
        },
      ];

      transactions.forEach((transaction) => {
        const typeIndex = transaction.type === 'INBOUND' ? 0 : 1;
        transactionTypes[typeIndex].count += 1;
        transactionTypes[typeIndex].amount += Number(transaction.amount);
      });

      // Calculate recent trend
      let currentMonthStartDate,
        currentMonthEndDate,
        previousMonthStartDate,
        previousMonthEndDate;

      if (selectedYear === currentYear) {
        // For current year, use current month and previous month
        const currentMonth = currentDate.getMonth();
        const previousMonth = currentMonth === 0 ? 11 : currentMonth - 1;
        const previousMonthYear =
          currentMonth === 0 ? currentYear - 1 : currentYear;

        currentMonthStartDate = new Date(currentYear, currentMonth, 1);
        currentMonthEndDate = new Date(currentYear, currentMonth + 1, 0);

        previousMonthStartDate = new Date(previousMonthYear, previousMonth, 1);
        previousMonthEndDate = new Date(
          previousMonthYear,
          previousMonth + 1,
          0
        );
      } else {
        // For past years, use December and November
        currentMonthStartDate = new Date(selectedYear, 11, 1); // December
        currentMonthEndDate = new Date(selectedYear, 11, 31);

        previousMonthStartDate = new Date(selectedYear, 10, 1); // November
        previousMonthEndDate = new Date(selectedYear, 10, 30);
      }

      const currentMonthTransactions = transactions.filter(
        (t) =>
          new Date(t.createdAt) >= currentMonthStartDate &&
          new Date(t.createdAt) <= currentMonthEndDate
      ).length;

      const previousMonthTransactions = transactions.filter(
        (t) =>
          new Date(t.createdAt) >= previousMonthStartDate &&
          new Date(t.createdAt) <= previousMonthEndDate
      ).length;

      let trendPercent = 0;
      let trendDirection: 'up' | 'down' = 'up';

      if (previousMonthTransactions > 0) {
        const diff = currentMonthTransactions - previousMonthTransactions;
        trendPercent = Math.round(
          Math.abs((diff / previousMonthTransactions) * 100)
        );
        trendDirection = diff >= 0 ? 'up' : 'down';
      } else if (currentMonthTransactions > 0) {
        trendPercent = 100;
        trendDirection = 'up';
      }

      // Generate monthly data
      const monthlyData = Array.from({ length: 12 }, (_, i) => ({
        month: months[i],
        inbound: 0,
        outbound: 0,
        inboundAmount: 0,
        outboundAmount: 0,
      }));

      transactions.forEach((transaction) => {
        const month = new Date(transaction.createdAt).getMonth();

        if (transaction.type === 'INBOUND') {
          monthlyData[month].inbound += 1;
          monthlyData[month].inboundAmount += Number(transaction.amount);
        } else if (transaction.type === 'OUTBOUND') {
          monthlyData[month].outbound += 1;
          monthlyData[month].outboundAmount += Number(transaction.amount);
        }
      });

      return {
        data: {
          year: selectedYear,
          totalTransactions,
          totalAmount,
          transactionTypes,
          recentTrend: {
            direction: trendDirection,
            percent: trendPercent,
          },
          monthlyData,
        },
      };
    } catch (error) {
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch transaction analytics', 500);
    }
  },

  getBookingsGraph: async (staffId: any, query: any) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(PERMISSIONS.PACKAGE_VIEW);
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }

    const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
    const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

    let locationFilter: any = {};
    if (hasLocationAll) {
      // No location filter - get all data
    } else if (hasRegion) {
      const regionId = await auth.getRegionId();
      locationFilter = {
        packageLocation: {
          regionId,
        },
      };
    } else {
      const locationId = await auth.getLocationId();
      locationFilter = { packageLocationId: locationId };
    }
    let year = query.year;
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth(); // 0-based index

    if (!year || isNaN(Number(year))) {
      year = currentYear.toString();
    }

    const startDate = new Date(`${year}-01-01T00:00:00.000Z`);
    const endDate = new Date(`${Number(year) + 1}-01-01T00:00:00.000Z`);

    const bookings = await db.packageBooking.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lt: endDate,
        },
        ...locationFilter,
      },
      select: {
        createdAt: true,
        bookingStatus: true,
      },
    });

    const monthlyData = Array.from({ length: 12 }, (_, i) => ({
      month: months[i],
      completed: 0,
      draft: 0,
    }));

    bookings.forEach((booking) => {
      const month = new Date(booking.createdAt).getMonth();
      if (booking.bookingStatus === 'COMPLETED') {
        monthlyData[month].completed += 1;
      } else if (booking.bookingStatus === 'DRAFT') {
        monthlyData[month].draft += 1;
      }
    });

    const resultData =
      Number(year) === currentYear
        ? monthlyData.slice(0, currentMonth + 1)
        : monthlyData;

    const completedThisMonth = monthlyData[currentMonth]?.completed || 0;
    const completedLastMonth = monthlyData[currentMonth - 1]?.completed || 0;

    let percentChange: number | null = null;
    let direction: 'up' | 'down' | null = null;

    if (completedLastMonth > 0) {
      const diff = completedThisMonth - completedLastMonth;
      percentChange = (diff / completedLastMonth) * 100;
      direction = percentChange >= 0 ? 'up' : 'down';
    } else if (completedThisMonth > 0) {
      percentChange = 100;
      direction = 'up';
    }

    return {
      year: Number(year),
      chartData: resultData,
      completedTrend: {
        percent: percentChange !== null ? Math.abs(percentChange) : null,
        direction,
      },
    };
  },

  getAllBookingsStatistics: async (staffId: any) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(PERMISSIONS.PACKAGE_VIEW);
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }

    const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
    if (!hasLocationAll) {
      throw new HttpError('Unauthorized', 403);
    }

    const bookings = await db.packageBooking.groupBy({
      by: ['location'],
      _count: {
        location: true,
      },
    });

    const chartData = bookings.map((booking) => {
      const rawLocation =
        booking.location?.split(',')[0].trim().toLowerCase() || 'unknown';

      return {
        location: rawLocation,
        bookings: booking._count.location,
        fill: `var(--color-${rawLocation})`,
      };
    });

    const totalBookings = chartData.reduce((sum, loc) => sum + loc.bookings, 0);

    return {
      chartData,
      totalBookings,
    };
  },

  getDashboardAnalytics: async (staffId: any, query: any) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(PERMISSIONS.PACKAGE_VIEW);
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }

    const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
    const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

    let locationFilter: any = {};
    if (hasLocationAll) {
      // No location filter - get all data
    } else if (hasRegion) {
      const regionId = await auth.getRegionId();
      locationFilter = {
        packageLocation: {
          regionId,
        },
      };
    } else {
      const locationId = await auth.getLocationId();
      locationFilter = { packageLocationId: locationId };
    }

    // Get current date information and year from query
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();

    // Get year from query or use current year as default
    let selectedYear = query?.year;
    if (!selectedYear || isNaN(Number(selectedYear))) {
      selectedYear = currentYear;
    } else {
      selectedYear = Number(selectedYear);
    }

    const currentMonth = currentDate.getMonth();
    const previousMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const previousMonthYear =
      currentMonth === 0 ? currentYear - 1 : currentYear;

    // Create date range for the selected year
    const yearStart = new Date(selectedYear, 0, 1);
    const yearEnd = new Date(selectedYear, 11, 31, 23, 59, 59);

    // Get booking counts by status directly from the database, filtered by year
    const [
      totalBookingsCount,
      completedBookingsCount,
      pendingBookingsCount,
      draftBookingsCount,
    ] = await Promise.all([
      db.packageBooking.count({
        where: {
          createdAt: {
            gte: yearStart,
            lte: yearEnd,
          },
          ...locationFilter,
        },
      }),
      db.packageBooking.count({
        where: {
          bookingStatus: 'COMPLETED',
          createdAt: {
            gte: yearStart,
            lte: yearEnd,
          },
          ...locationFilter,
        },
      }),
      db.packageBooking.count({
        where: {
          bookingStatus: 'PENDING',
          createdAt: {
            gte: yearStart,
            lte: yearEnd,
          },
          ...locationFilter,
        },
      }),
      db.packageBooking.count({
        where: {
          bookingStatus: 'DRAFT',
          createdAt: {
            gte: yearStart,
            lte: yearEnd,
          },
          ...locationFilter,
        },
      }),
    ]);

    // We'll fetch specific data as needed for each section

    // Set booking statistics from database counts
    const totalBookings = totalBookingsCount;
    const completedBookings = completedBookingsCount;
    const pendingBookings = pendingBookingsCount;
    const draftBookings = draftBookingsCount;

    // Calculate revenue statistics from database for the selected year
    const revenueData = await db.packageBooking.aggregate({
      _sum: {
        totalAmount: true,
      },
      where: {
        bookingStatus: {
          in: ['COMPLETED', 'PENDING'],
        },
        createdAt: {
          gte: yearStart,
          lte: yearEnd,
        },
        ...locationFilter,
      },
    });

    const totalRevenue = Number(revenueData._sum.totalAmount || 0);

    const averageBookingValue =
      completedBookings + pendingBookings > 0
        ? Number(
            (totalRevenue / (completedBookings + pendingBookings)).toFixed(2)
          )
        : 0;

    const conversionRate =
      totalBookings > 0
        ? Math.round((completedBookings / totalBookings) * 100)
        : 0;

    // Calculate trend based on selected year
    let currentMonthStartDate,
      currentMonthEndDate,
      previousMonthStartDate,
      previousMonthEndDate;

    if (selectedYear === currentYear) {
      // For current year, use current month and previous month
      currentMonthStartDate = new Date(currentYear, currentMonth, 1);
      currentMonthEndDate = new Date(currentYear, currentMonth + 1, 0);

      previousMonthStartDate = new Date(previousMonthYear, previousMonth, 1);
      previousMonthEndDate = new Date(previousMonthYear, previousMonth + 1, 0);
    } else {
      // For past years, use December and November
      currentMonthStartDate = new Date(selectedYear, 11, 1); // December
      currentMonthEndDate = new Date(selectedYear, 11, 31);

      previousMonthStartDate = new Date(selectedYear, 10, 1); // November
      previousMonthEndDate = new Date(selectedYear, 10, 30);
    }

    const [currentMonthBookings, previousMonthBookings] = await Promise.all([
      db.packageBooking.count({
        where: {
          createdAt: {
            gte: currentMonthStartDate,
            lte: currentMonthEndDate,
          },
          bookingStatus: 'COMPLETED',
          ...locationFilter,
        },
      }),
      db.packageBooking.count({
        where: {
          createdAt: {
            gte: previousMonthStartDate,
            lte: previousMonthEndDate,
          },
          bookingStatus: 'COMPLETED',
          ...locationFilter,
        },
      }),
    ]);

    let trendPercent = 0;
    let trendDirection: 'up' | 'down' = 'up';

    if (previousMonthBookings > 0) {
      const diff = currentMonthBookings - previousMonthBookings;
      trendPercent = Math.round(Math.abs((diff / previousMonthBookings) * 100));
      trendDirection = diff >= 0 ? 'up' : 'down';
    } else if (currentMonthBookings > 0) {
      trendPercent = 100;
      trendDirection = 'up';
    }

    // Initialize monthly data structure
    const monthlyData = Array.from({ length: 12 }, (_, i) => ({
      month: months[i],
      completed: 0,
      pending: 0,
      draft: 0,
      revenue: 0,
    }));

    // Get all bookings for the selected year
    const yearBookings = await db.packageBooking.findMany({
      where: {
        createdAt: {
          gte: yearStart,
          lte: yearEnd,
        },
        ...locationFilter,
      },
      select: {
        createdAt: true,
        bookingStatus: true,
        totalAmount: true,
      },
    });

    // Process the bookings to populate monthly data
    yearBookings.forEach((booking) => {
      const month = new Date(booking.createdAt).getMonth();

      if (booking.bookingStatus === 'COMPLETED') {
        monthlyData[month].completed += 1;
        monthlyData[month].revenue += Number(booking.totalAmount || 0);
      } else if (booking.bookingStatus === 'PENDING') {
        monthlyData[month].pending += 1;
      } else if (booking.bookingStatus === 'DRAFT') {
        monthlyData[month].draft += 1;
      }
    });

    // Get location data from database for the selected year
    const locationBookings = await db.packageBooking.findMany({
      where: {
        location: {
          not: null,
        },
        createdAt: {
          gte: yearStart,
          lte: yearEnd,
        },
        ...locationFilter,
      },
      select: {
        location: true,
        bookingStatus: true,
        totalAmount: true,
      },
    });

    // Process location data
    const locationMap = new Map();

    locationBookings.forEach((booking) => {
      if (!booking.location) return;

      const location = booking.location.split(',')[0].trim();

      if (!locationMap.has(location)) {
        locationMap.set(location, { bookings: 0, revenue: 0 });
      }

      const locationData = locationMap.get(location);
      locationData.bookings += 1;

      if (
        booking.bookingStatus === 'COMPLETED' ||
        booking.bookingStatus === 'PENDING'
      ) {
        locationData.revenue += Number(booking.totalAmount || 0);
      }
    });

    const locationData = Array.from(locationMap.entries())
      .map(([location, data]) => ({
        location,
        bookings: data.bookings,
        revenue: data.revenue,
      }))
      .sort((a, b) => b.bookings - a.bookings);

    // Status distribution is already calculated
    const statusDistribution = [
      { status: 'Completed', count: completedBookings },
      { status: 'Pending', count: pendingBookings },
      { status: 'Draft', count: draftBookings },
    ];

    // Get package data from database for the selected year
    const packageBookings = await db.packageBooking.findMany({
      where: {
        createdAt: {
          gte: yearStart,
          lte: yearEnd,
        },
        ...locationFilter,
      },
      select: {
        bookingStatus: true,
        totalAmount: true,
        packageId: true,
      },
    });

    // Get all packages to ensure we have names
    const packages = await db.package.findMany({
      select: {
        id: true,
        name: true,
      },
    });

    // Create a map of package IDs to names for quick lookup
    const packageNameMap = new Map();
    packages.forEach((pkg) => {
      packageNameMap.set(pkg.id, pkg.name);
    });

    // Process package data
    const packageMap = new Map();

    packageBookings.forEach((booking) => {
      const packageName = packageNameMap.get(booking.packageId);
      if (!packageName) return;

      if (!packageMap.has(packageName)) {
        packageMap.set(packageName, { bookings: 0, revenue: 0 });
      }

      const packageData = packageMap.get(packageName);
      packageData.bookings += 1;

      if (
        booking.bookingStatus === 'COMPLETED' ||
        booking.bookingStatus === 'PENDING'
      ) {
        packageData.revenue += Number(booking.totalAmount || 0);
      }
    });

    const topPackages = Array.from(packageMap.entries())
      .map(([name, data]) => ({
        name,
        bookings: data.bookings,
        revenue: data.revenue,
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    // Get weekday distribution from database for the selected year
    const allBookings = await db.packageBooking.findMany({
      where: {
        createdAt: {
          gte: yearStart,
          lte: yearEnd,
        },
        ...locationFilter,
      },
      select: {
        createdAt: true,
      },
    });

    // Process weekday data
    const weekdays = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ];
    const weekdayDistribution = weekdays.map((day) => ({ day, bookings: 0 }));

    allBookings.forEach((booking) => {
      const dayOfWeek = new Date(booking.createdAt).getDay();
      weekdayDistribution[dayOfWeek].bookings += 1;
    });

    return {
      data: {
        year: selectedYear,
        totalBookings,
        completedBookings,
        pendingBookings,
        draftBookings,
        totalRevenue,
        averageBookingValue,
        conversionRate,
        recentTrend: {
          direction: trendDirection,
          percent: trendPercent,
        },
        monthlyData,
        locationData,
        statusDistribution,
        topPackages,
        weekdayDistribution,
      },
    };
  },

  getFeedbackAnalytics: async (staffId: any, query: any) => {
    try {
      const auth = createStaffAuthHelper(staffId);

      const canManage = await auth.hasPermission(PERMISSIONS.FEEDBACK_VIEW);
      if (!canManage) {
        throw new HttpError('Unauthorized', 403);
      }

      const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
      const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

      let locationFilter: any = {};
      if (hasLocationAll) {
        // No location filter - get all data
      } else if (hasRegion) {
        const regionId = await auth.getRegionId();
        locationFilter = {
          location: {
            regionId,
          },
        };
      } else {
        const locationId = await auth.getLocationId();
        locationFilter = { locationId };
      }

      // Get year from query or use current year as default
      let selectedYear = query?.year;
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();

      if (!selectedYear || isNaN(Number(selectedYear))) {
        selectedYear = currentYear;
      } else {
        selectedYear = Number(selectedYear);
      }

      // Create date range for the selected year
      const yearStart = new Date(selectedYear, 0, 1);
      const yearEnd = new Date(selectedYear, 11, 31, 23, 59, 59);

      // Get all feedback for the selected year
      const feedbacks = await db.feedback.findMany({
        where: {
          createdAt: {
            gte: yearStart,
            lte: yearEnd,
          },
          ...locationFilter,
        },
        select: {
          id: true,
          createdAt: true,
          rating: true,
          type: true,
        },
      });

      // Calculate total feedbacks
      const totalFeedbacks = feedbacks.length;

      // Calculate average rating
      const totalRating = feedbacks.reduce(
        (sum, feedback) => sum + feedback.rating,
        0
      );
      const averageRating =
        totalFeedbacks > 0
          ? Number((totalRating / totalFeedbacks).toFixed(1))
          : 0;

      // Calculate rating distribution
      const ratingDistribution = [
        { rating: 1, count: 0 },
        { rating: 2, count: 0 },
        { rating: 3, count: 0 },
        { rating: 4, count: 0 },
        { rating: 5, count: 0 },
      ];

      feedbacks.forEach((feedback) => {
        const ratingIndex = feedback.rating - 1;
        if (ratingIndex >= 0 && ratingIndex < 5) {
          ratingDistribution[ratingIndex].count += 1;
        }
      });

      // Calculate feedback types distribution
      const typeMap = new Map();

      feedbacks.forEach((feedback) => {
        const type = feedback.type || 'Unknown';

        if (!typeMap.has(type)) {
          typeMap.set(type, { count: 0 });
        }

        const typeData = typeMap.get(type);
        typeData.count += 1;
      });

      const feedbackTypes = Array.from(typeMap.entries())
        .map(([type, data]) => ({
          type,
          count: data.count,
        }))
        .sort((a, b) => b.count - a.count);

      // Calculate recent trend
      let currentMonthStartDate,
        currentMonthEndDate,
        previousMonthStartDate,
        previousMonthEndDate;

      if (selectedYear === currentYear) {
        // For current year, use current month and previous month
        const currentMonth = currentDate.getMonth();
        const previousMonth = currentMonth === 0 ? 11 : currentMonth - 1;
        const previousMonthYear =
          currentMonth === 0 ? currentYear - 1 : currentYear;

        currentMonthStartDate = new Date(currentYear, currentMonth, 1);
        currentMonthEndDate = new Date(currentYear, currentMonth + 1, 0);

        previousMonthStartDate = new Date(previousMonthYear, previousMonth, 1);
        previousMonthEndDate = new Date(
          previousMonthYear,
          previousMonth + 1,
          0
        );
      } else {
        // For past years, use December and November
        currentMonthStartDate = new Date(selectedYear, 11, 1); // December
        currentMonthEndDate = new Date(selectedYear, 11, 31);

        previousMonthStartDate = new Date(selectedYear, 10, 1); // November
        previousMonthEndDate = new Date(selectedYear, 10, 30);
      }

      const currentMonthFeedbacks = feedbacks.filter(
        (f) =>
          new Date(f.createdAt) >= currentMonthStartDate &&
          new Date(f.createdAt) <= currentMonthEndDate
      ).length;

      const previousMonthFeedbacks = feedbacks.filter(
        (f) =>
          new Date(f.createdAt) >= previousMonthStartDate &&
          new Date(f.createdAt) <= previousMonthEndDate
      ).length;

      let trendPercent = 0;
      let trendDirection: 'up' | 'down' = 'up';

      if (previousMonthFeedbacks > 0) {
        const diff = currentMonthFeedbacks - previousMonthFeedbacks;
        trendPercent = Math.round(
          Math.abs((diff / previousMonthFeedbacks) * 100)
        );
        trendDirection = diff >= 0 ? 'up' : 'down';
      } else if (currentMonthFeedbacks > 0) {
        trendPercent = 100;
        trendDirection = 'up';
      }

      // Generate monthly data
      const monthlyData = Array.from({ length: 12 }, (_, i) => ({
        month: months[i],
        count: 0,
      }));

      feedbacks.forEach((feedback) => {
        const month = new Date(feedback.createdAt).getMonth();
        monthlyData[month].count += 1;
      });

      return {
        data: {
          year: selectedYear,
          totalFeedbacks,
          averageRating,
          ratingDistribution,
          feedbackTypes,
          recentTrend: {
            direction: trendDirection,
            percent: trendPercent,
          },
          monthlyData,
        },
      };
    } catch (error) {
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch feedback analytics', 500);
    }
  },

  getStaffReferralAnalytics: async (staffId: any, query: any) => {
    try {
      const auth = createStaffAuthHelper(staffId);

      const canManage = await auth.hasPermission(PERMISSIONS.REWARD_VIEW);
      if (!canManage) {
        throw new HttpError('Unauthorized', 403);
      }

      const hasLocationAll = await auth.hasPermission(PERMISSIONS.LOCATION_ALL);
      const hasRegion = await auth.hasPermission(PERMISSIONS.LOCATION_REGION);

      let locationFilter: any = {};
      if (hasLocationAll) {
        // No location filter - get all data
      } else if (hasRegion) {
        const regionId = await auth.getRegionId();
        locationFilter = {
          location: {
            regionId,
          },
        };
      } else {
        const locationId = await auth.getLocationId();
        locationFilter = { locationId };
      }

      // Get year from query or use current year as default
      let selectedYear = query?.year;
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();

      if (!selectedYear || isNaN(Number(selectedYear))) {
        selectedYear = currentYear;
      } else {
        selectedYear = Number(selectedYear);
      }

      // Create date range for the selected year
      const yearStart = new Date(selectedYear, 0, 1);
      const yearEnd = new Date(selectedYear, 11, 31, 23, 59, 59);

      // Get all referral code usages for the selected year
      const referralUsages = await db.referralCodeUsage.findMany({
        where: {
          dateUsed: {
            gte: yearStart,
            lte: yearEnd,
          },
          ...locationFilter,
        },
        include: {
          referralCode: {
            include: {
              assignedToStaff: {
                select: {
                  id: true,
                  fullName: true,
                  location: true,
                },
              },
            },
          },
        },
      });

      // Calculate total referrals and commission
      const totalReferrals = referralUsages.length;
      const totalCommission = referralUsages.reduce(
        (sum, usage) => sum + Number(usage.value),
        0
      );

      // Get active staff with referral codes
      const activeStaff = await db.staff.count({
        where: {
          isActive: true,
          referralCode: {
            isActive: true,
          },
          ...locationFilter,
        },
      });

      // Calculate recent trend
      let currentMonthStartDate,
        currentMonthEndDate,
        previousMonthStartDate,
        previousMonthEndDate;

      if (selectedYear === currentYear) {
        // For current year, use current month and previous month
        const currentMonth = currentDate.getMonth();
        const previousMonth = currentMonth === 0 ? 11 : currentMonth - 1;
        const previousMonthYear =
          currentMonth === 0 ? currentYear - 1 : currentYear;

        currentMonthStartDate = new Date(currentYear, currentMonth, 1);
        currentMonthEndDate = new Date(currentYear, currentMonth + 1, 0);

        previousMonthStartDate = new Date(previousMonthYear, previousMonth, 1);
        previousMonthEndDate = new Date(
          previousMonthYear,
          previousMonth + 1,
          0
        );
      } else {
        // For past years, use December and November
        currentMonthStartDate = new Date(selectedYear, 11, 1); // December
        currentMonthEndDate = new Date(selectedYear, 11, 31);

        previousMonthStartDate = new Date(selectedYear, 10, 1); // November
        previousMonthEndDate = new Date(selectedYear, 10, 30);
      }

      const currentMonthReferrals = referralUsages.filter(
        (usage) =>
          new Date(usage.dateUsed) >= currentMonthStartDate &&
          new Date(usage.dateUsed) <= currentMonthEndDate
      ).length;

      const previousMonthReferrals = referralUsages.filter(
        (usage) =>
          new Date(usage.dateUsed) >= previousMonthStartDate &&
          new Date(usage.dateUsed) <= previousMonthEndDate
      ).length;

      let trendPercent = 0;
      let trendDirection: 'up' | 'down' = 'up';

      if (previousMonthReferrals > 0) {
        const diff = currentMonthReferrals - previousMonthReferrals;
        trendPercent = Math.round(
          Math.abs((diff / previousMonthReferrals) * 100)
        );
        trendDirection = diff >= 0 ? 'up' : 'down';
      } else if (currentMonthReferrals > 0) {
        trendPercent = 100;
        trendDirection = 'up';
      }

      // Calculate top staff
      const staffMap = new Map();

      referralUsages.forEach((usage) => {
        const staff = usage.referralCode?.assignedToStaff;
        if (!staff) return;

        const staffId = staff.id;

        if (!staffMap.has(staffId)) {
          staffMap.set(staffId, {
            name: staff.fullName,
            referrals: 0,
            commission: 0,
            location: staff.location || 'Unknown',
          });
        }

        const staffData = staffMap.get(staffId);
        staffData.referrals += 1;
        staffData.commission += Number(usage.value);
      });

      const topStaff = Array.from(staffMap.values())
        .sort((a, b) => b.commission - a.commission)
        .slice(0, 5);

      // Calculate location data
      const locationMap = new Map();

      referralUsages.forEach((usage) => {
        const staff = usage.referralCode?.assignedToStaff;
        if (!staff || !staff.location) return;

        const location = staff.location;

        if (!locationMap.has(location)) {
          locationMap.set(location, {
            location,
            referrals: 0,
            commission: 0,
          });
        }

        const locationData = locationMap.get(location);
        locationData.referrals += 1;
        locationData.commission += Number(usage.value);
      });

      const locationData = Array.from(locationMap.values()).sort(
        (a, b) => b.referrals - a.referrals
      );

      // Generate monthly data
      const monthlyData = Array.from({ length: 12 }, (_, i) => ({
        month: months[i],
        referrals: 0,
        commission: 0,
      }));

      referralUsages.forEach((usage) => {
        const month = new Date(usage.dateUsed).getMonth();
        monthlyData[month].referrals += 1;
        monthlyData[month].commission += Number(usage.value);
      });

      return {
        data: {
          year: selectedYear,
          totalReferrals,
          activeStaff,
          totalCommission,
          recentTrend: {
            direction: trendDirection,
            percent: trendPercent,
          },
          topStaff,
          locationData,
          monthlyData,
        },
      };
    } catch (error) {
      if (error instanceof HttpError) {
        throw error;
      }
      throw new HttpError('Failed to fetch staff referral analytics', 500);
    }
  },
};
