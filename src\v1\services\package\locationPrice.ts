import { db } from '../../utils/model';
import { HttpError } from '../../utils/httpError';
import {
  staffHasPermission,
  PERMISSIONS,
  createStaffAuthHelper,
} from '../../utils/permission';

export const locationPriceService = {
  getAllPackageLocationPrice: async () => {
    const results = await db.packageLocationPrice.findMany({
      select: {
        id: true,
        package: {
          select: {
            name: true,
          },
        },
        location: {
          select: {
            name: true,
            region: true,
          },
        },
      },
    });

    const formatted = results.map((item) => ({
      id: item.id,
      name: `${item.package.name} - (${item.location.name}-${item.location.region})`,
    }));

    return formatted;
  },

  updatePackageLocationPrices: async (staffId: any, reqBody: any) => {
    const auth = createStaffAuthHelper(staffId);

    const canManage = await auth.hasPermission(PERMISSIONS.PACKAGE_EDIT);
    if (!canManage) {
      throw new HttpError('Unauthorized', 403);
    }
    const locationId = await auth.getLocationId();
    await staffHasPermission(staffId, PERMISSIONS.PACKAGE_EDIT);
    interface PriceInput {
      id?: number;
      currency: string;
      amount: string;
      endDate: string;
      location: {
        id: number;
        name: string;
      };
    }
    const { packageId, prices } = reqBody;

    const existingPackage = await db.package.findUnique({
      where: { id: Number(packageId) },
      include: {
        packageLocationPrices: true,
      },
    });

    if (!existingPackage) {
      throw new HttpError('Package does not exist', 400);
    }

    const incomingIds = prices
      .filter((p: PriceInput) => p.id !== undefined)
      .map((p: PriceInput) => p.id);

    const existingIds = existingPackage.packageLocationPrices.map((p) => p.id);

    const idsToDelete = existingIds.filter((id) => !incomingIds.includes(id));

    const operations = prices.map((priceData: PriceInput) => {
      const { id, currency, amount, endDate, location } = priceData;

      if (id) {
        return db.packageLocationPrice.update({
          where: { id: Number(id) },
          data: {
            currency,
            amount,
            endDate: new Date(endDate).toISOString(),
            locationId: location.id,
          },
        });
      } else {
        return db.packageLocationPrice.create({
          data: {
            currency,
            amount,
            endDate: new Date(endDate).toISOString(),
            locationId: location.id,
            packageId: Number(packageId),
          },
        });
      }
    });

    // Step 1: prepare everything needed for deletion
    const deletionPlans = await Promise.all(
      idsToDelete.map(async (id) => {
        const bookingCount = await db.packageBooking.count({
          where: { packageLocationId: Number(id) },
        });

        const relatedModifiers = await db.packageLocationPrice.findUnique({
          where: { id },
          select: {
            modifiers: {
              select: { id: true },
            },
          },
        });

        const modifierIds =
          relatedModifiers?.modifiers.map((pm) => pm.id) ?? [];

        const unusedModifierIds: number[] = [];

        for (const modId of modifierIds) {
          const usageCount = await db.packageLocationPrice.count({
            where: {
              modifiers: {
                some: { id: modId },
              },
            },
          });

          if (usageCount === 0) {
            unusedModifierIds.push(modId);
          }
        }

        return {
          id,
          bookingCount,
          modifierIds,
          unusedModifierIds,
        };
      })
    );

    // Step 2: build Prisma operations (no async or await in map!)
    const deleteOperations = deletionPlans.flatMap(
      ({ id, bookingCount, modifierIds, unusedModifierIds }) => {
        if (bookingCount === 0) {
          return [
            // Disconnect modifiers
            db.packageLocationPrice.update({
              where: { id },
              data: {
                modifiers: {
                  disconnect: modifierIds.map((modId) => ({ id: modId })),
                },
              },
            }),
            // Delete unused modifiers
            ...unusedModifierIds.map((modId) =>
              db.packagePriceModifier.delete({
                where: { id: modId },
              })
            ),
            // Delete the price
            db.packageLocationPrice.delete({
              where: { id },
            }),
          ];
        } else {
          // Soft delete
          return db.packageLocationPrice.update({
            where: { id },
            data: {
              deletedAt: new Date(),
            },
          });
        }
      }
    );

    await db.$transaction([...operations, ...deleteOperations]);

    return { message: 'Package location prices updated successfully.' };
  },
};
