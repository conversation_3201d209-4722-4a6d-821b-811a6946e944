import { Router } from 'express';
import { packageRoute } from './router/package.route';
import { locationRoute } from './router/location.route';
import { staffRoute } from './router/staff.route';
import { rewardRoute } from './router/reward.route';
import { bookingRoute } from './router/booking.route';
import { transactionRoute } from './router/transaction.route';
import { analyticsRoute } from './router/analytic.route';
import { discountRoute } from './router/discount.route';
import { feedbackRoute } from './router/feedback.route';
import { settingsRoute } from './router/settings.route';
import { referralRoute } from './router/referral.route';
import messagingRoute from './router/messaging.route';
import cafeteriaRoute from './router/cafeteria.route';
import { forumRoute } from './router/forum.route';
import { gameMainRoute } from './router/game.route';
import { notificationRoute } from './router/notification.route';
import { fileUploadRoute } from './router/upload/fileUpload.route';

import {
  successResponse,
  errorResponse,
  globalErrorResponse,
} from '../middleware/response';

export const routes = Router();

routes.use(successResponse);
routes.use(errorResponse);
routes.use(globalErrorResponse);

routes.use('/location', locationRoute);
routes.use('/package', packageRoute);
routes.use('/staff', staffRoute);
routes.use('/reward', rewardRoute);
routes.use('/booking', bookingRoute);
routes.use('/transaction', transactionRoute);
routes.use('/analytic', analyticsRoute);
routes.use('/feedback', feedbackRoute);
routes.use('/discount', discountRoute);
routes.use('/settings', settingsRoute);
routes.use('/referral', referralRoute);
routes.use('/messaging', messagingRoute);
routes.use('/cafeteria', cafeteriaRoute);
routes.use('/forum', forumRoute);
routes.use('/games', gameMainRoute);
routes.use('/notifications', notificationRoute);
routes.use('/upload', fileUploadRoute);
